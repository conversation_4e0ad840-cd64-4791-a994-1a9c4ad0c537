def ACM(img, p, q, m):
    counter = 0

    if img.mode == "P":
        img = img.convert("RGB")

    assert img.size[0] == img.size[1]

    while counter < m:
        dim = width, height = img.size

        with Image.new(img.mode, dim) as canvas:
            for x in range(width):
                for y in range(height):
                    nx = (x + y * p) % width
                    ny = (x * q + y * (p * q + 1)) % height

                    canvas.putpixel((nx, ny), img.getpixel((x, y)))

        img = canvas
        counter += 1

    return canvas

# My image was encrypted by ACM ,  but I lost the p ,q  and m ......