from PIL import Image
import numpy as np

def ACM_encrypt(img, p, q, m):
    """Arnold Cat Map encryption"""
    counter = 0

    if img.mode == "P":
        img = img.convert("RGB")

    assert img.size[0] == img.size[1]

    while counter < m:
        dim = width, height = img.size
        canvas = Image.new(img.mode, dim)
        
        for x in range(width):
            for y in range(height):
                nx = (x + y * p) % width
                ny = (x * q + y * (p * q + 1)) % height
                canvas.putpixel((nx, ny), img.getpixel((x, y)))

        img = canvas
        counter += 1

    return canvas

def ACM_decrypt(img, p, q, m):
    """Arnold Cat Map decryption - reverse the transformation"""
    counter = 0

    if img.mode == "P":
        img = img.convert("RGB")

    assert img.size[0] == img.size[1]

    while counter < m:
        dim = width, height = img.size
        canvas = Image.new(img.mode, dim)

        for x in range(width):
            for y in range(height):
                # Inverse Arnold Cat Map transformation
                # Original: nx = (x + y * p) % width
                #          ny = (x * q + y * (p * q + 1)) % height
                # Inverse: we need to find original (x, y) from current (nx, ny)
                nx = ((p * q + 1) * x - p * y) % width
                ny = (-q * x + y) % height
                canvas.putpixel((nx, ny), img.getpixel((x, y)))

        img = canvas
        counter += 1

    return canvas

def brute_force_acm(encrypted_img, max_p=10, max_q=10, max_m=20):
    """Brute force to find p, q, m parameters"""
    print("Starting brute force attack...")
    
    for p in range(1, max_p + 1):
        for q in range(1, max_q + 1):
            for m in range(1, max_m + 1):
                try:
                    decrypted = ACM_decrypt(encrypted_img.copy(), p, q, m)
                    # Save the result to check manually
                    filename = f"decrypted_p{p}_q{q}_m{m}.png"
                    decrypted.save(filename)
                    print(f"Saved: {filename}")
                except Exception as e:
                    print(f"Error with p={p}, q={q}, m={m}: {e}")

def create_test_image():
    """Create a simple test image to verify the algorithm"""
    size = 64
    img = Image.new('RGB', (size, size), 'white')

    # Create a simple pattern
    for x in range(size):
        for y in range(size):
            if (x + y) % 8 < 4:
                img.putpixel((x, y), (255, 0, 0))  # Red
            else:
                img.putpixel((x, y), (0, 0, 255))  # Blue

    return img

def analyze_zip_for_image():
    """Analyze the zip file more thoroughly for hidden image data"""
    import zipfile

    with zipfile.ZipFile('9fd71368-dcb8-4c37-a135-7c5d2a882f71.zip', 'r') as z:
        for info in z.infolist():
            print(f"File: {info.filename}")
            print(f"Size: {info.file_size}")
            print(f"Compressed size: {info.compress_size}")

            # Read the file content
            with z.open(info.filename) as f:
                content = f.read()

            # Check if the content might contain image data
            print(f"Content length: {len(content)}")

            # Look for potential image dimensions in the content
            # Arnold Cat Map requires square images
            possible_sizes = [32, 64, 128, 256, 512]
            for size in possible_sizes:
                expected_bytes = size * size * 3  # RGB
                if len(content) >= expected_bytes:
                    print(f"Could contain {size}x{size} RGB image")

if __name__ == "__main__":
    # Look for encrypted image files
    import os
    import glob

    print("Looking for image files...")
    image_files = glob.glob("*.png") + glob.glob("*.jpg") + glob.glob("*.jpeg") + glob.glob("*.bmp")

    if image_files:
        print(f"Found image files: {image_files}")
        for img_file in image_files:
            print(f"Processing {img_file}...")
            img = Image.open(img_file)
            brute_force_acm(img)
    else:
        print("No image files found in current directory")
        print("Let's analyze the zip file for hidden image data...")
        analyze_zip_for_image()

        print("\nCreating test image to verify algorithm...")
        test_img = create_test_image()
        test_img.save("test_original.png")

        # Test encryption with known parameters
        print("Testing encryption with p=1, q=1, m=1...")
        encrypted = ACM_encrypt(test_img.copy(), 1, 1, 1)
        encrypted.save("test_encrypted.png")

        # Test decryption
        print("Testing decryption...")
        decrypted = ACM_decrypt(encrypted.copy(), 1, 1, 1)
        decrypted.save("test_decrypted.png")

        print("Test files saved. Check if decryption works correctly.")
